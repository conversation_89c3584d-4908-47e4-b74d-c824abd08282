import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  try {
    const { isOnShift, action = 'toggle', endReason = 'manual' } = await request.json()
    console.log('Driver status API called with:', { isOnShift, action, endReason })

    // For simplified system, we only handle shift status
    if (action === 'toggle' && typeof isOnShift !== 'boolean') {
      return NextResponse.json(
        { error: "isOnShift must be a boolean value for toggle action" },
        { status: 400 }
      )
    }

    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id, name')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active')
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    if (!driverProfile.is_verified || !driverProfile.is_active) {
      return NextResponse.json(
        { error: "Driver not verified or inactive" },
        { status: 403 }
      )
    }

    // Check current status
    const { data: currentStatus } = await supabase
      .from('driver_status')
      .select('is_on_shift, is_on_delivery, current_order_id')
      .eq('driver_id', driverProfile.id)
      .single()

    // Prevent going offline while on delivery
    if (!isOnline && currentStatus?.is_on_delivery) {
      return NextResponse.json(
        { error: "Cannot go offline while on a delivery" },
        { status: 409 }
      )
    }

    // Handle different actions with simplified shift logic
    let shiftId = null
    let updatedStatus = null

    if (action === 'toggle') {
      // Simple on-shift/off-shift toggle
      if (isOnShift) {
        // Start shift
        const { data: shiftResult, error: shiftError } = await supabase
          .rpc('start_driver_shift', { p_driver_id: driverProfile.id })

        if (shiftError) {
          console.error('Error starting shift:', shiftError)
          return NextResponse.json(
            { error: shiftError.message || "Failed to start shift" },
            { status: 500 }
          )
        }
        shiftId = shiftResult
      } else {
        // End shift with reason
        const { data: endResult, error: endError } = await supabase
          .rpc('end_driver_shift', {
            p_driver_id: driverProfile.id,
            p_end_reason: endReason
          })

        if (endError) {
          console.error('Error ending shift:', endError)
          return NextResponse.json(
            { error: endError.message || "Failed to end shift" },
            { status: 500 }
          )
        }
      }

    } else if (action === 'start_shift') {
      // Explicit start shift
      console.log('Starting shift for driver:', driverProfile.id)
      const { data: shiftResult, error: shiftError } = await supabase
        .rpc('start_driver_shift', { p_driver_id: driverProfile.id })

      console.log('Shift start result:', { shiftResult, shiftError })
      if (shiftError) {
        console.error('Error starting shift:', shiftError)
        return NextResponse.json(
          { error: shiftError.message || "Failed to start shift" },
          { status: 500 }
        )
      }
      shiftId = shiftResult

    } else if (action === 'end_shift') {
      // Explicit end shift with reason
      const { data: endResult, error: endError } = await supabase
        .rpc('end_driver_shift', {
          p_driver_id: driverProfile.id,
          p_end_reason: endReason
        })

      if (endError) {
        console.error('Error ending shift:', endError)
        return NextResponse.json(
          { error: endError.message || "Failed to end shift" },
          { status: 500 }
        )
      }
    }

    // Get updated driver status
    console.log('Fetching updated status for driver:', driverProfile.id)
    const { data: statusData, error: statusFetchError } = await supabase
      .from('driver_status')
      .select('*')
      .eq('driver_id', driverProfile.id)
      .single()

    console.log('Status fetch result:', { statusData, statusFetchError })
    if (statusFetchError) {
      console.error('Error fetching updated status:', statusFetchError)
      return NextResponse.json(
        { error: "Failed to fetch updated status" },
        { status: 500 }
      )
    }

    updatedStatus = statusData

    // Log driver activity with simplified shift context
    let activityType = 'status_change'
    if (action === 'start_shift' || (action === 'toggle' && updatedStatus.is_on_shift)) {
      activityType = 'shift_started'
    } else if (action === 'end_shift' || (action === 'toggle' && !updatedStatus.is_on_shift)) {
      activityType = endReason === 'manual' ? 'shift_ended_manual' :
                    endReason === 'app_closed' ? 'shift_ended_app_closed' :
                    endReason === 'disconnection_timeout' ? 'shift_ended_timeout' : 'shift_ended'
    }

    const { error: activityError } = await supabase
      .from('driver_activity_log')
      .insert({
        driver_id: driverProfile.id,
        activity_type: activityType,
        details: {
          previousShiftStatus: currentStatus?.is_on_shift || false,
          newShiftStatus: updatedStatus.is_on_shift,
          action: action,
          endReason: endReason,
          shiftId: shiftId,
          locationCleared: !updatedStatus.is_on_shift,
          timestamp: new Date().toISOString()
        },
        created_at: new Date().toISOString()
      })

    if (activityError) {
      console.error('Error logging driver activity:', activityError)
      // Don't fail the request if activity logging fails
    }

    // Get available orders count if on shift
    let availableOrdersCount = 0
    if (updatedStatus.is_on_shift) {
      const { count } = await supabase
        .from('orders')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'ready')
        .is('driver_id', null)

      availableOrdersCount = count || 0
    }

    // Generate appropriate message based on action and final state
    let message = ''
    if (action === 'start_shift') {
      message = `${user.name} has started their shift and is accepting orders`
    } else if (action === 'end_shift') {
      message = `${user.name} has ended their shift but remains online`
    } else if (action === 'toggle' && updatedStatus.is_online) {
      message = `${user.name} is now online`
    } else if (action === 'toggle' && !updatedStatus.is_online) {
      message = `${user.name} is now offline`
    }

    return NextResponse.json({
      success: true,
      message,
      action,
      status: {
        isOnShift: updatedStatus.is_on_shift,
        isOnDelivery: updatedStatus.is_on_delivery,
        currentOrderId: updatedStatus.current_order_id,
        lastStatusChange: updatedStatus.last_status_change,
        hasLocation: !!(updatedStatus.current_location_lat && updatedStatus.current_location_lng),
        locationCleared: !updatedStatus.is_on_shift
      },
      shiftId: shiftId,
      endReason: endReason,
      availableOrders: updatedStatus.is_on_shift ? availableOrdersCount : 0,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in update driver status API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id, name')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active')
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    // Get current driver status
    const { data: driverStatus } = await supabase
      .from('driver_status')
      .select('*')
      .eq('driver_id', driverProfile.id)
      .single()

    return NextResponse.json({
      status: {
        isOnShift: driverStatus?.is_on_shift || false,
        isOnDelivery: driverStatus?.is_on_delivery || false,
        currentOrderId: driverStatus?.current_order_id || null,
        lastStatusChange: driverStatus?.last_status_change || null,
        hasLocation: !!(driverStatus?.current_location_lat && driverStatus?.current_location_lng),
        location: driverStatus?.current_location_lat && driverStatus?.current_location_lng ? {
          latitude: driverStatus.current_location_lat,
          longitude: driverStatus.current_location_lng,
          updatedAt: driverStatus.location_updated_at
        } : null
      },
      driver: {
        id: driverProfile.id,
        name: user.name,
        isVerified: driverProfile.is_verified,
        isActive: driverProfile.is_active
      }
    })

  } catch (error) {
    console.error('Error in get driver status API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
