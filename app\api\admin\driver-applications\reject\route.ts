import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function POST(request: NextRequest) {
  try {
    const { driverId, reason } = await request.json()

    if (!driverId || !reason) {
      return NextResponse.json(
        { error: "Driver ID and rejection reason are required" },
        { status: 400 }
      )
    }

    // Get session for admin user info
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Get driver profile information for notification
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('first_name, last_name, email, vehicle_type')
      .eq('id', driverId)
      .single()

    if (driverError || !driverProfile) {
      console.error('Error fetching driver profile:', driverError)
      return NextResponse.json(
        { error: "Driver not found" },
        { status: 404 }
      )
    }

    // Update driver profile to rejected (inactive)
    const { error: updateError } = await supabase
      .from('driver_profiles')
      .update({
        is_verified: false,
        is_active: false,
        notes: `REJECTED: ${reason}\n\n${new Date().toISOString()}\n\n--- Previous Notes ---\n`,
        updated_at: new Date().toISOString()
      })
      .eq('id', driverId)

    if (updateError) {
      console.error('Error updating driver profile:', updateError)
      return NextResponse.json(
        { error: "Failed to reject driver" },
        { status: 500 }
      )
    }

    // Log the rejection activity
    const { error: activityError } = await supabase
      .from('driver_activity_log')
      .insert({
        driver_id: driverId,
        activity_type: 'application_rejected',
        timestamp: new Date().toISOString(),
        notes: `Driver application rejected by Loop admin. Reason: ${reason}`
      })

    if (activityError) {
      console.error('Error logging driver activity:', activityError)
      // Don't fail the request for this, just log it
    }

    // Create admin notification for driver rejection
    try {
      const { data: adminUsers } = await supabase
        .from('users')
        .select('id')
        .eq('role', 'admin')
        .neq('id', session.user.id) // Don't notify the admin who performed the action

      if (adminUsers && adminUsers.length > 0) {
        const notifications = adminUsers.map(admin => ({
          admin_user_id: admin.id,
          type: 'driver_rejected',
          title: 'Driver Application Rejected',
          message: `${driverProfile.first_name} ${driverProfile.last_name} has been rejected as a driver by ${session.user.email}`,
          action_url: `/admin/driver-applications/${driverId}`,
          priority: 'medium',
          related_table: 'driver_profiles',
          related_record_id: driverId,
          metadata: {
            driver_name: `${driverProfile.first_name} ${driverProfile.last_name}`,
            driver_email: driverProfile.email,
            vehicle_type: driverProfile.vehicle_type,
            rejection_reason: reason,
            rejected_by: session.user.email
          }
        }))

        await supabase
          .from('admin_notifications')
          .insert(notifications)

        console.log(`Created admin notifications for ${adminUsers.length} admin users`)
      }
    } catch (notificationError) {
      console.error('Error creating admin notification:', notificationError)
      // Don't fail the request if notification fails
    }

    // TODO: Send rejection email to driver
    // For now, we'll just log that an email should be sent
    console.log(`TODO: Send rejection email for driver ${driverId}. Reason: ${reason}`)

    // In a real implementation, you would send an email here:
    /*
    await sendDriverRejectionEmail({
      driverId: driverId,
      reason: reason,
      reapplyUrl: `${process.env.NEXT_PUBLIC_APP_URL}/partners/riders/apply`
    })
    */

    return NextResponse.json({
      success: true,
      message: "Driver application rejected",
      driverId: driverId
    })

  } catch (error) {
    console.error('Error in driver rejection:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// TODO: Implement email sending function
/*
async function sendDriverRejectionEmail({ driverId, reason, reapplyUrl }: {
  driverId: string
  reason: string
  reapplyUrl: string
}) {
  // Implementation would use your email service (e.g., SendGrid, AWS SES, etc.)
  // Email template would include:
  // - Polite rejection message
  // - Specific reason for rejection
  // - Instructions for reapplying (if applicable)
  // - Contact information for questions
}
*/
