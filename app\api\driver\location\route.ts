import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  try {
    const { latitude, longitude } = await request.json()

    if (!latitude || !longitude) {
      return NextResponse.json(
        { error: "Latitude and longitude are required" },
        { status: 400 }
      )
    }

    // Validate coordinates are within reasonable bounds
    if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
      return NextResponse.json(
        { error: "Invalid coordinates" },
        { status: 400 }
      )
    }

    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active')
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    if (!driverProfile.is_verified || !driverProfile.is_active) {
      return NextResponse.json(
        { error: "Driver not verified or inactive" },
        { status: 403 }
      )
    }

    // Update driver location in driver_status table (only if on shift)
    // First check if driver is on shift
    const { data: currentStatus } = await supabase
      .from('driver_status')
      .select('is_on_shift')
      .eq('driver_id', driverProfile.id)
      .single()

    if (!currentStatus?.is_on_shift) {
      return NextResponse.json(
        { error: "Location tracking only available when on shift" },
        { status: 403 }
      )
    }

    const { data: updatedStatus, error: updateError } = await supabase
      .from('driver_status')
      .update({
        current_location_lat: latitude,
        current_location_lng: longitude,
        location_updated_at: new Date().toISOString(),
        last_status_change: new Date().toISOString()
      })
      .eq('driver_id', driverProfile.id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating driver location:', updateError)
      return NextResponse.json(
        { error: "Failed to update location" },
        { status: 500 }
      )
    }

    // Log driver activity
    const { error: activityError } = await supabase
      .from('driver_activity_log')
      .insert({
        driver_id: driverProfile.id,
        activity_type: 'location_update',
        details: {
          latitude,
          longitude,
          timestamp: new Date().toISOString()
        },
        created_at: new Date().toISOString()
      })

    if (activityError) {
      console.error('Error logging driver activity:', activityError)
      // Don't fail the request if activity logging fails
    }

    return NextResponse.json({
      success: true,
      message: "Location updated successfully",
      location: {
        latitude,
        longitude,
        updatedAt: updatedStatus.location_updated_at
      },
      status: {
        isOnline: updatedStatus.is_online,
        isOnDelivery: updatedStatus.is_on_delivery,
        currentOrderId: updatedStatus.current_order_id
      }
    })

  } catch (error) {
    console.error('Error in update location API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
