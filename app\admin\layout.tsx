"use client"

import { useEffect, useState } from "react"
import { Inter } from "next/font/google"
import type { ReactNode } from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { useAuthDirect, AuthProviderDirect } from "@/context/auth-context-direct"
import { SupabaseProvider } from "@/components/providers/supabase-provider"
import { AdminGuard } from "@/components/auth/auth-guards"
import { usePendingDriverApplications } from "@/hooks/use-pending-driver-applications"
import { usePendingBusinessRequests } from "@/hooks/use-pending-business-requests"
import { useAdminNotifications } from "@/hooks/use-admin-notifications"
import UserMenu from "@/components/auth/user-menu"
import WheelLogoIcon from "@/components/wheel-logo-icon"
import {
  BarChart3,
  Home,
  Settings,
  Users,
  Bell,
  MenuIcon,
  Store,
  ShoppingBag,
  MapPin,
  Truck,
  Clock,
  CheckCircle,
  ExternalLink,
  AlertCircle,
  Heart,
  Building2,
  Lightbulb,
  Globe
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap"
})

interface AdminLayoutProps {
  children: ReactNode
}

function AdminLayoutContent({ children }: AdminLayoutProps) {
  const pathname = usePathname()
  const router = useRouter()
  const { user, userProfile } = useAuthDirect()
  const { pendingCount } = usePendingDriverApplications()
  const { pendingCount: pendingBusinessRequestsCount } = usePendingBusinessRequests()
  const { notifications, unreadCount, markAsRead, markAllAsRead } = useAdminNotifications(userProfile?.id)

  // Get page title based on pathname
  const getPageTitle = (path: string) => {
    const routes: Record<string, string> = {
      '/admin': 'Dashboard',
      '/admin/businesses': 'Businesses',
      '/admin/orders': 'Orders',
      '/admin/business-requests': 'Business Requests',
      '/admin/requests/businesses': 'Jersey Business Requests',
      '/admin/requests/features': 'Feature Requests',
      '/admin/requests/areas': 'Area Requests',
      '/admin/requests/services': 'Service Requests',
      '/admin/requests/non-jersey-businesses': 'International Business Requests',
      '/admin/users': 'Users',
      '/admin/driver-applications': 'Driver Applications',
      '/admin/analytics': 'Analytics',
      '/admin/analytics/drivers': 'Driver Analytics',
      '/admin/update-coordinates': 'Update Coordinates',
      '/admin/notifications': 'Notifications',
      '/admin/settings': 'Settings'
    }
    return routes[path] || 'Admin'
  }

  return (
    <div className={`${inter.variable} font-sans flex min-h-screen bg-gray-100`}>
      {/* Sidebar - Desktop */}
      <aside className="hidden md:flex flex-col w-64 bg-white border-r shadow-sm">
        <div className="p-4 border-b">
          <Link href="/" className="flex items-center group">
            <div className="flex items-center bg-emerald-600 rounded-lg px-4 py-2.5 border border-emerald-500 shadow-sm h-10">
              <div className="wheel-logo mr-2 group-hover:animate-spin">
                <WheelLogoIcon
                  size={24}
                  color="white"
                  className="text-white w-6 h-6"
                />
              </div>
              <span className="text-lg font-bold text-white">Loop</span>
            </div>

          </Link>
        </div>

        <nav className="flex-1 p-4 space-y-2">
          {/* Main Navigation */}
          <div className="space-y-1">
            <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-3 py-2">
              Main
            </div>
            <Link
              href="/admin"
              className={`flex items-center px-3 py-2.5 text-sm rounded-lg transition-colors ${
                pathname === "/admin"
                  ? "text-emerald-700 bg-emerald-100 font-medium border-l-4 border-emerald-600"
                  : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              }`}
            >
              <Home className={`w-5 h-5 mr-3 ${pathname === "/admin" ? "text-emerald-600" : "text-gray-400"}`} />
              Dashboard
            </Link>
            <Link
              href="/admin/businesses"
              className={`flex items-center justify-between px-3 py-2.5 text-sm rounded-lg transition-colors ${
                pathname === "/admin/businesses"
                  ? "text-emerald-700 bg-emerald-100 font-medium border-l-4 border-emerald-600"
                  : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              }`}
              title="manage_businesses permission"
            >
              <div className="flex items-center">
                <Store className={`w-5 h-5 mr-3 ${pathname === "/admin/businesses" ? "text-emerald-600" : "text-gray-400"}`} />
                Businesses
              </div>
            </Link>
            <Link
              href="/admin/orders"
              className={`flex items-center justify-between px-3 py-2.5 text-sm rounded-lg transition-colors ${
                pathname === "/admin/orders"
                  ? "text-emerald-700 bg-emerald-100 font-medium border-l-4 border-emerald-600"
                  : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              }`}
            >
              <div className="flex items-center">
                <ShoppingBag className={`w-5 h-5 mr-3 ${pathname === "/admin/orders" ? "text-emerald-600" : "text-gray-400"}`} />
                Orders
              </div>
            </Link>
            {/* Requests Section */}
            <div className="space-y-1">
              <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-3 py-2">
                Requests
              </div>
              <Link
                href="/admin/requests/businesses"
                className={`flex items-center justify-between px-3 py-2.5 text-sm rounded-lg transition-colors ${
                  pathname === "/admin/requests/businesses"
                    ? "text-emerald-700 bg-emerald-100 font-medium border-l-4 border-emerald-600"
                    : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                }`}
                title="manage_businesses permission"
              >
                <div className="flex items-center">
                  <Building2 className={`w-5 h-5 mr-3 ${pathname === "/admin/requests/businesses" ? "text-emerald-600" : "text-gray-400"}`} />
                  Businesses
                </div>
                {pendingBusinessRequestsCount > 0 && (
                  <Badge variant="destructive" className="text-xs h-5 min-w-[20px] flex items-center justify-center">
                    {pendingBusinessRequestsCount > 99 ? '99+' : pendingBusinessRequestsCount}
                  </Badge>
                )}
              </Link>
              <Link
                href="/admin/requests/features"
                className={`flex items-center px-3 py-2.5 text-sm rounded-lg transition-colors ${
                  pathname === "/admin/requests/features"
                    ? "text-emerald-700 bg-emerald-100 font-medium border-l-4 border-emerald-600"
                    : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                }`}
                title="manage_businesses permission"
              >
                <div className="flex items-center">
                  <Lightbulb className={`w-5 h-5 mr-3 ${pathname === "/admin/requests/features" ? "text-emerald-600" : "text-gray-400"}`} />
                  Features
                </div>
              </Link>
              <Link
                href="/admin/requests/areas"
                className={`flex items-center px-3 py-2.5 text-sm rounded-lg transition-colors ${
                  pathname === "/admin/requests/areas"
                    ? "text-emerald-700 bg-emerald-100 font-medium border-l-4 border-emerald-600"
                    : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                }`}
                title="manage_businesses permission"
              >
                <div className="flex items-center">
                  <MapPin className={`w-5 h-5 mr-3 ${pathname === "/admin/requests/areas" ? "text-emerald-600" : "text-gray-400"}`} />
                  Delivery Areas
                </div>
              </Link>
              <Link
                href="/admin/requests/services"
                className={`flex items-center px-3 py-2.5 text-sm rounded-lg transition-colors ${
                  pathname === "/admin/requests/services"
                    ? "text-emerald-700 bg-emerald-100 font-medium border-l-4 border-emerald-600"
                    : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                }`}
                title="manage_businesses permission"
              >
                <div className="flex items-center">
                  <Truck className={`w-5 h-5 mr-3 ${pathname === "/admin/requests/services" ? "text-emerald-600" : "text-gray-400"}`} />
                  Services
                </div>
              </Link>
              <Link
                href="/admin/requests/non-jersey-businesses"
                className={`flex items-center px-3 py-2.5 text-sm rounded-lg transition-colors ${
                  pathname === "/admin/requests/non-jersey-businesses"
                    ? "text-emerald-700 bg-emerald-100 font-medium border-l-4 border-emerald-600"
                    : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                }`}
                title="manage_businesses permission"
              >
                <div className="flex items-center">
                  <Globe className={`w-5 h-5 mr-3 ${pathname === "/admin/requests/non-jersey-businesses" ? "text-emerald-600" : "text-gray-400"}`} />
                  International
                </div>
              </Link>
            </div>
            <Link
              href="/admin/users"
              className={`flex items-center px-3 py-2.5 text-sm rounded-lg transition-colors ${
                pathname === "/admin/users"
                  ? "text-emerald-700 bg-emerald-100 font-medium border-l-4 border-emerald-600"
                  : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              }`}
              title="manage_users permission"
            >
              <Users className={`w-5 h-5 mr-3 ${pathname === "/admin/users" ? "text-emerald-600" : "text-gray-400"}`} />
              Users
            </Link>
            <Link
              href="/admin/driver-applications"
              className={`flex items-center justify-between px-3 py-2.5 text-sm rounded-lg transition-colors ${
                pathname === "/admin/driver-applications"
                  ? "text-emerald-700 bg-emerald-100 font-medium border-l-4 border-emerald-600"
                  : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              }`}
              title="approve_drivers permission"
            >
              <div className="flex items-center">
                <Truck className={`w-5 h-5 mr-3 ${pathname === "/admin/driver-applications" ? "text-emerald-600" : "text-gray-400"}`} />
                Driver Applications
              </div>
              {pendingCount > 0 && (
                <Badge variant="destructive" className="text-xs h-5 min-w-[20px] flex items-center justify-center">
                  {pendingCount > 99 ? '99+' : pendingCount}
                </Badge>
              )}
            </Link>
          </div>

          {/* Analytics & Tools */}
          <div className="space-y-1 pt-4">
            <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-3 py-2">
              Analytics & Tools
            </div>
            <Link
              href="/admin/analytics"
              className={`flex items-center px-3 py-2.5 text-sm rounded-lg transition-colors ${
                pathname === "/admin/analytics"
                  ? "text-emerald-700 bg-emerald-100 font-medium border-l-4 border-emerald-600"
                  : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              }`}
              title="view_analytics permission"
            >
              <BarChart3 className={`w-5 h-5 mr-3 ${pathname === "/admin/analytics" ? "text-emerald-600" : "text-gray-400"}`} />
              Analytics
            </Link>
            <Link
              href="/admin/analytics/drivers"
              className={`flex items-center px-3 py-2.5 text-sm rounded-lg transition-colors ${
                pathname === "/admin/analytics/drivers"
                  ? "text-emerald-700 bg-emerald-100 font-medium border-l-4 border-emerald-600"
                  : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              }`}
              title="view_analytics permission"
            >
              <Users className={`w-5 h-5 mr-3 ${pathname === "/admin/analytics/drivers" ? "text-emerald-600" : "text-gray-400"}`} />
              Driver Analytics
            </Link>
            <Link
              href="/admin/update-coordinates"
              className={`flex items-center px-3 py-2.5 text-sm rounded-lg transition-colors ${
                pathname === "/admin/update-coordinates"
                  ? "text-emerald-700 bg-emerald-100 font-medium border-l-4 border-emerald-600"
                  : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              }`}
              title="manage_businesses permission"
            >
              <MapPin className={`w-5 h-5 mr-3 ${pathname === "/admin/update-coordinates" ? "text-emerald-600" : "text-gray-400"}`} />
              Update Coordinates
            </Link>
          </div>

          {/* System */}
          <div className="space-y-1 pt-4">
            <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-3 py-2">
              System
            </div>
            <Link
              href="/admin/notifications"
              className={`flex items-center justify-between px-3 py-2.5 text-sm rounded-lg transition-colors ${
                pathname === "/admin/notifications"
                  ? "text-emerald-700 bg-emerald-100 font-medium border-l-4 border-emerald-600"
                  : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              }`}
              title="access_admin_panel permission"
            >
              <div className="flex items-center">
                <Bell className={`w-5 h-5 mr-3 ${pathname === "/admin/notifications" ? "text-emerald-600" : "text-gray-400"}`} />
                Notifications
              </div>
              {unreadCount > 0 && (
                <Badge variant="destructive" className="text-xs h-5 min-w-[20px] flex items-center justify-center">
                  {unreadCount > 99 ? '99+' : unreadCount}
                </Badge>
              )}
            </Link>
            <Link
              href="/admin/audit-logs"
              className={`flex items-center px-3 py-2.5 text-sm rounded-lg transition-colors ${
                pathname === "/admin/audit-logs"
                  ? "text-emerald-700 bg-emerald-100 font-medium border-l-4 border-emerald-600"
                  : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              }`}
              title="access_admin_panel permission"
            >
              <div className="flex items-center">
                <FileText className={`w-5 h-5 mr-3 ${pathname === "/admin/audit-logs" ? "text-emerald-600" : "text-gray-400"}`} />
                Audit Logs
              </div>
            </Link>
            <Link
              href="/admin/settings"
              className={`flex items-center px-3 py-2.5 text-sm rounded-lg transition-colors ${
                pathname === "/admin/settings"
                  ? "text-emerald-700 bg-emerald-100 font-medium border-l-4 border-emerald-600"
                  : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              }`}
              title="access_admin_panel permission"
            >
              <Settings className={`w-5 h-5 mr-3 ${pathname === "/admin/settings" ? "text-emerald-600" : "text-gray-400"}`} />
              Settings
            </Link>
          </div>
        </nav>


      </aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Navigation */}
        <header className="bg-white border-b border-gray-200 shadow-sm">
          <div className="container-fluid">
            <div className="flex items-center justify-between py-3">
              {/* Left side - Logo */}
              <div className="flex items-center">
                <Link href="/" className="flex items-center group">
                  <div className="flex items-center bg-emerald-600 rounded-lg px-4 py-2.5 border border-emerald-500 shadow-sm h-10">
                    <div className="wheel-logo mr-2 group-hover:animate-spin">
                      <WheelLogoIcon
                        size={24}
                        color="white"
                        className="text-white w-6 h-6"
                      />
                    </div>
                    <span className="text-lg font-bold text-white">Loop</span>
                  </div>
                </Link>
              </div>

              {/* Right side - Notifications and Account Menu */}
              <div className="flex items-center ml-auto space-x-3">
                {/* Notifications */}
                <Button variant="outline" className="h-10 px-4 font-medium" asChild>
                  <Link href="/admin/notifications">
                    <Bell className="h-4 w-4 mr-2" />
                    <span className="hidden sm:inline">Notifications</span>
                    {unreadCount > 0 && (
                      <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] h-[18px] flex items-center justify-center">
                        {unreadCount > 9 ? '9+' : unreadCount}
                      </span>
                    )}
                  </Link>
                </Button>

                {/* Account Menu */}
                <div className="hidden md:block">
                  <UserMenu />
                </div>

                {/* Mobile Menu Button */}
                <div className="md:hidden">
                  <Button variant="ghost" size="icon" className="h-10 w-10 rounded-lg">
                    <MenuIcon className="h-6 w-6" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-y-auto p-4 md:p-6">{children}</main>
      </div>
    </div>
  )
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  return (
    <SupabaseProvider>
      <AuthProviderDirect>
        <AdminLayoutContent>{children}</AdminLayoutContent>
      </AuthProviderDirect>
    </SupabaseProvider>
  )
}
