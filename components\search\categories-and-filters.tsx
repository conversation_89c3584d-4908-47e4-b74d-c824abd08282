"use client"

import { useState, useRef, useEffect } from 'react'
import { ChevronDown, ChevronLeft } from 'lucide-react'
import CategoryItem from './category-item'

interface CategoriesAndFiltersProps {
  onCategorySelect?: (category: string) => void
  onFilterSelect?: (filter: string) => void
  resultsCount?: number
  businessType?: string
  selectedCategories?: string[]
}

// Simple category interface for our new system
interface SimpleCategory {
  id: number
  name: string
  slug: string
  business_type_id: number | null
  category_purpose: string
  description: string | null
  display_order: number
  level: number
  parent_id: number | null
}

// Category with icon for display
interface CategoryWithIcon extends SimpleCategory {
  icon: string
}

// Map of category slugs to emoji icons - Enhanced with Uber Eats style categories
const categoryIcons: Record<string, string> = {
  // General categories (Level 1)
  'food-and-drink': '🍽️',
  'health-and-beauty': '💅',
  'household': '🏠',

  // Level 2 categories
  'restaurant-food': '🍲',
  'cafe-items': '🧁',
  'grocery': '🛒',
  'beverages': '🧃',
  'pharmacy-items': '💊',
  'personal-care': '🧼',
  'cleaning-supplies': '🧽',
  'home-essentials': '🛋️',

  // Restaurant categories - Uber Eats style
  'burgers': '🍔',
  'pizza': '🍕',
  'sushi': '🍣',
  'italian': '🍝',
  'mexican': '🌮',
  'seafood': '🍤',
  'vegetarian': '🥗',
  'desserts': '🍰',
  'chinese': '🥡',
  'indian': '🍛',
  'fast-food': '🍟',
  'halal': '🌶️',
  'wings': '🍗',
  'boneless-wings': '🍗',
  'classic-wings': '🐔',
  'healthy': '🥗',
  'starters': '🍤',
  'main-courses': '🍽️',
  'sides': '🍟',
  'combos': '🍱',
  'grill': '🔥',
  'british': '🥧',
  'french': '🥖',
  'mediterranean': '🫒',
  'appetizers': '🧆',
  'soups': '🍜',

  // Additional Uber Eats popular categories
  'american': '🇺🇸',
  'bbq': '🍖',
  'chicken': '🍗',
  'sandwich': '🥪',
  'salad': '🥗',
  'noodles': '🍜',
  'thai': '🍛',
  'korean': '🍲',
  'japanese': '🍱',
  'vietnamese': '🍜',
  'greek': '🫒',
  'turkish': '🥙',
  'middle-eastern': '🥙',
  'lebanese': '🥙',
  'moroccan': '🍲',
  'ethiopian': '🍛',
  'caribbean': '🌶️',
  'latin-american': '🌮',
  'peruvian': '🍤',
  'brazilian': '🥩',
  'argentinian': '🥩',
  'steakhouse': '🥩',
  'tapas': '🍤',
  'ramen': '🍜',
  'pho': '🍜',
  'dumplings': '🥟',
  'fried-chicken': '🍗',
  'fish-and-chips': '🍟',
  'kebab': '🥙',
  'falafel': '🧆',
  'shawarma': '🥙',

  // Cafe categories
  'coffee': '☕',
  'coffee-drinks': '☕',
  'pastries': '🥐',
  'sandwiches': '🥪',
  'tea': '🍵',
  'breakfast': '🍳',
  'lunch': '🥪',
  'smoothies': '🧃',
  'bubble-tea': '🧋',

  // Shop/Grocery categories
  'fresh-produce': '🍎',
  'dairy-eggs': '🥚',
  'dairy': '🥚',
  'bakery': '🥖',
  'meat-seafood': '🥩',
  'pantry-staples': '🧂',
  'alcohol': '🍺',
  'alcoholic-beverages': '🍺',
  'convenience': '🍿',
  'snacks': '🍫',
  'meat': '🥩',
  'seafood': '🍤',
  'produce': '🥬',
  'groceries': '🥬',
  'frozen': '🧊',
  'canned-goods': '🥫',
  'home-goods': '🛋️',

  // Beverage categories
  'soft-drinks': '🥤',
  'water': '💧',
  'juices': '🍊',
  'drinks': '🧋',

  // Pharmacy categories
  'medications': '💊',
  'health': '🩺',
  'beauty': '💄',
  'first-aid': '🩹',
  'vitamins': '💊',
  'vitamins-supplements': '💊',
  'skincare': '🧴',
  'hair-care': '💇',
  'oral-care': '🪥',
  'baby-care': '👶',

  // Errand categories
  'delivery': '🚚',
  'pickup': '🛍️',
  'shopping': '🛍️',
  'services': '🔧',
  'tasks': '📋',
  'prescription': '💊',
  'wellness': '🧘',

  // Default icon for unknown categories
  'default': '📋'
}

interface CategoryPath {
  id: number;
  name: string;
  level: number;
}

export default function CategoriesAndFilters({
  onCategorySelect,
  onFilterSelect,
  resultsCount,
  businessType = 'all',
  selectedCategories = []
}: CategoriesAndFiltersProps) {
  const [categories, setCategories] = useState<CategoryWithIcon[]>([])
  const [loading, setLoading] = useState(true)
  const [currentLevel, setCurrentLevel] = useState(1)
  const [categoryPath, setCategoryPath] = useState<CategoryPath[]>([])
  const [currentParentId, setCurrentParentId] = useState<number | null>(null)

  // Fetch categories based on business type and current navigation state
  useEffect(() => {
    async function fetchCategories() {
      setLoading(true)
      try {
        let categoriesData: any[] = [];

        // Map business type slug to ID
        const businessTypeMap: Record<string, number> = {
          'restaurant': 1,
          'shop': 2,
          'pharmacy': 3,
          'cafe': 4,
          'errand': 38
        };

        const businessTypeId = businessType !== 'all' ? businessTypeMap[businessType] : null;

        if (currentParentId) {
          // Fetch Level 2 categories (children of selected Level 1 category)
          const params = new URLSearchParams({
            purpose: 'specialization',
            level: '2',
            parentId: currentParentId.toString()
          });

          if (businessTypeId) {
            params.append('businessTypeId', businessTypeId.toString());
          }

          const response = await fetch(`/api/categories?${params}`);
          if (response.ok) {
            const data = await response.json();
            categoriesData = data.categories || [];
          }
        } else {
          // Fetch Level 1 categories (parent categories)
          const params = new URLSearchParams({
            purpose: 'specialization',
            level: '1'
          });

          if (businessTypeId) {
            params.append('businessTypeId', businessTypeId.toString());
          }

          const response = await fetch(`/api/categories?${params}`);
          if (response.ok) {
            const data = await response.json();
            categoriesData = data.categories || [];
          }
        }

        // Add icons to categories with simple mapping
        const categoriesWithIcons = categoriesData.map((cat: SimpleCategory) => {
          const nameLower = cat.name.toLowerCase();
          let icon = categoryIcons.default;

          // Enhanced icon mapping based on category name - Uber Eats style
          if (nameLower.includes('pizza')) icon = '🍕';
          else if (nameLower.includes('burger')) icon = '🍔';
          else if (nameLower.includes('sushi') || nameLower.includes('japanese')) icon = '🍣';
          else if (nameLower.includes('chinese') || nameLower.includes('asian')) icon = '🥡';
          else if (nameLower.includes('italian')) icon = '🍝';
          else if (nameLower.includes('mexican') || nameLower.includes('latin')) icon = '🌮';
          else if (nameLower.includes('indian') || nameLower.includes('curry')) icon = '🍛';
          else if (nameLower.includes('thai')) icon = '🍛';
          else if (nameLower.includes('korean')) icon = '🍲';
          else if (nameLower.includes('vietnamese') || nameLower.includes('pho')) icon = '🍜';
          else if (nameLower.includes('american')) icon = '🇺🇸';
          else if (nameLower.includes('british') || nameLower.includes('fish') && nameLower.includes('chips')) icon = '🥧';
          else if (nameLower.includes('french')) icon = '🥖';
          else if (nameLower.includes('mediterranean') || nameLower.includes('greek')) icon = '🫒';
          else if (nameLower.includes('middle') || nameLower.includes('lebanese') || nameLower.includes('turkish')) icon = '🥙';
          else if (nameLower.includes('seafood') || nameLower.includes('fish')) icon = '🍤';
          else if (nameLower.includes('bbq') || nameLower.includes('barbecue')) icon = '🍖';
          else if (nameLower.includes('steak') || nameLower.includes('grill')) icon = '🥩';
          else if (nameLower.includes('chicken') || nameLower.includes('wings')) icon = '🍗';
          else if (nameLower.includes('sandwich') || nameLower.includes('sub')) icon = '🥪';
          else if (nameLower.includes('salad') || nameLower.includes('healthy') || nameLower.includes('vegetarian') || nameLower.includes('vegan')) icon = '🥗';
          else if (nameLower.includes('noodles') || nameLower.includes('ramen') || nameLower.includes('soup')) icon = '🍜';
          else if (nameLower.includes('dessert') || nameLower.includes('cake') || nameLower.includes('sweet')) icon = '🍰';
          else if (nameLower.includes('kebab') || nameLower.includes('shawarma')) icon = '🥙';
          else if (nameLower.includes('dumpling')) icon = '🥟';
          else if (nameLower.includes('tapas')) icon = '🍤';
          else if (nameLower.includes('pub') || nameLower.includes('bar')) icon = '🍺';
          else if (nameLower.includes('fast') || nameLower.includes('quick')) icon = '🍟';
          else if (nameLower.includes('italian')) icon = '🍝';
          else if (nameLower.includes('coffee')) icon = '☕';
          else if (nameLower.includes('pastries') || nameLower.includes('baked')) icon = '🥐';
          else if (nameLower.includes('breakfast')) icon = '🍳';
          else if (nameLower.includes('healthy')) icon = '🥗';
          else if (nameLower.includes('groceries')) icon = '🛒';
          else if (nameLower.includes('produce')) icon = '🥬';
          else if (nameLower.includes('electronics')) icon = '📱';
          else if (nameLower.includes('clothing')) icon = '👕';
          else if (nameLower.includes('home')) icon = '🏠';
          else if (nameLower.includes('vitamin') || nameLower.includes('mineral')) icon = '💊';
          else if (nameLower.includes('prescription')) icon = '💊';
          else if (nameLower.includes('wellness')) icon = '🧘';
          else if (nameLower.includes('skincare')) icon = '🧴';
          else if (nameLower.includes('shopping')) icon = '🛍️';
          else if (nameLower.includes('delivery')) icon = '🚚';
          else if (nameLower.includes('airport')) icon = '✈️';
          else if (nameLower.includes('pet')) icon = '🐕';
          else if (nameLower.includes('task')) icon = '📋';

          return { ...cat, icon } as CategoryWithIcon;
        });

        // Sort by display_order
        const sortedCategories = categoriesWithIcons.sort((a, b) => a.display_order - b.display_order);

        setCategories(sortedCategories)
      } catch (error) {
        console.error('Error fetching categories:', error)
        // Fallback to empty categories
        setCategories([])
      } finally {
        setLoading(false)
      }
    }

    fetchCategories()
  }, [businessType, currentLevel, currentParentId])

  // Helper function to check if a category is selected
  const isCategorySelected = (categoryName: string): boolean => {
    return selectedCategories.includes(categoryName);
  };

  // Refs for scrolling
  const categoriesContainerRef = useRef<HTMLDivElement>(null);

  // Function to scroll categories container to the right
  const scrollCategoriesRight = () => {
    if (categoriesContainerRef.current) {
      categoriesContainerRef.current.scrollBy({
        left: 200,
        behavior: 'smooth'
      });
    }
  }

  // Function to scroll categories container to the left
  const scrollCategoriesLeft = () => {
    if (categoriesContainerRef.current) {
      categoriesContainerRef.current.scrollBy({
        left: -200,
        behavior: 'smooth'
      });
    }
  }

  // Handle category selection
  const handleCategoryClick = async (category: any) => {
    console.log('🔍 Category clicked:', category);

    // Always trigger the category selection callback for filtering
    if (onCategorySelect) {
      onCategorySelect(category.name);
    }

    // For Level 1 categories, optionally check if they have children for future navigation features
    if (category.level === 1) {
      const params = new URLSearchParams({
        purpose: 'specialization',
        level: '2',
        parentId: category.id.toString()
      });

      const response = await fetch(`/api/categories?${params}`);
      if (response.ok) {
        const data = await response.json();
        const children = data.categories || [];

        if (children.length > 0) {
          // Has children - could be used for future sub-category navigation
          console.log('🔍 Category has', children.length, 'sub-categories available');
        }
      }
    }
  }

  // Handle navigation back up the hierarchy
  const handleNavigateBack = () => {
    if (currentLevel === 1) return; // Already at top level

    // Go back to Level 1
    setCurrentParentId(null);
    setCurrentLevel(1);
    setCategoryPath([]);
    setSelectedCategory(null);
  }

  // Filter click handler removed as filters section is no longer displayed

  return (
    <div className="bg-white py-2">
      {/* Category Breadcrumb Navigation and Results Count */}
      <div className="flex items-center justify-between px-2 py-2 text-sm text-gray-600 border-b border-gray-100">
        {categoryPath.length > 0 ? (
          <div className="flex items-center">
            <button
              onClick={handleNavigateBack}
              className="flex items-center hover:text-emerald-600 transition-colors"
            >
              <ChevronLeft className="w-4 h-4 mr-1" />
              Back
            </button>
            <span className="mx-2">|</span>
            <div className="flex items-center overflow-x-auto scrollbar-hide">
              {categoryPath.map((item, index) => (
                <div key={`breadcrumb-${index}-${item.id}`} className="flex items-center whitespace-nowrap">
                  {index > 0 && <span className="mx-1">›</span>}
                  <button
                    onClick={() => {
                      // Navigate to this level in the breadcrumb
                      const newPath = categoryPath.slice(0, index + 1);
                      setCategoryPath(newPath);

                      // Set the current path string to this level's path
                      setCurrentPathString(item.path || '');

                      // Set the parent ID to this item's ID
                      setCurrentParentId(item.id);

                      // Set the current level
                      setCurrentLevel(item.level);

                      // Note: Selected categories are managed by parent component
                    }}
                    className="font-medium text-emerald-700 hover:text-emerald-900 hover:underline transition-colors"
                  >
                    {item.name}
                  </button>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div>{/* Empty div to maintain flex layout when no breadcrumb */}</div>
        )}

        {/* Results Count - moved here from the filters section */}
        {resultsCount !== undefined && (
          <div className="text-sm text-gray-500 whitespace-nowrap">
            Found {resultsCount} {resultsCount === 1 ? 'result' : 'results'}
          </div>
        )}
      </div>

      {/* Categories */}
      <div className="relative pt-8 pb-4">
        {/* Left scroll arrow */}
        <div className="absolute left-0 top-4 h-full flex items-center bg-gradient-to-r from-white via-white to-transparent w-16 pointer-events-none z-10">
          <button
            onClick={scrollCategoriesLeft}
            className="w-10 h-10 rounded-full bg-white shadow-md flex items-center justify-center ml-4 cursor-pointer hover:bg-gray-50 active:bg-gray-100 transition-colors pointer-events-auto border border-gray-100"
            aria-label="Scroll categories left"
          >
            <ChevronDown className="w-5 h-5 text-emerald-600" style={{ transform: 'rotate(90deg)' }} />
          </button>
        </div>

        <div
          ref={categoriesContainerRef}
          className="flex overflow-x-auto gap-5 pb-3 pl-20 pr-16 scrollbar-hide touch-pan-x"
          style={{
            WebkitOverflowScrolling: 'touch',
            scrollSnapType: 'x mandatory',
            paddingTop: '4px'
          }}
        >
          {loading ? (
            // Loading skeleton
            Array(8).fill(0).map((_, index) => (
              <div
                key={index}
                className="min-w-[90px] h-[76px] bg-gray-100 animate-pulse rounded-lg"
                style={{ scrollSnapAlign: 'center' }}
              />
            ))
          ) : categories.length > 0 ? (
            // Render fetched categories
            categories.map((category, index) => (
              <CategoryItem
                key={`category-${index}-${category.id}`}
                icon={category.icon}
                label={category.name}
                onClick={() => handleCategoryClick(category)}
                active={isCategorySelected(category.name)} // Highlight if selected
              />
            ))
          ) : (
            // Empty state - don't show anything if we're in a category path
            // This prevents "No categories available" from showing when a category has no children
            categoryPath.length > 0 ? null : (
              <div className="py-2 px-4 text-sm text-gray-500">
                No categories available
              </div>
            )
          )}
        </div>
        <div className="absolute right-0 top-4 h-full flex items-center bg-gradient-to-l from-white via-white to-transparent w-16 pointer-events-none">
          <button
            onClick={scrollCategoriesRight}
            className="w-10 h-10 rounded-full bg-white shadow-md flex items-center justify-center ml-auto cursor-pointer hover:bg-gray-50 active:bg-gray-100 transition-colors pointer-events-auto border border-gray-100"
            aria-label="Scroll categories right"
            style={{ marginRight: '4px' }}
          >
            <ChevronDown className="w-5 h-5 text-emerald-600" style={{ transform: 'rotate(270deg)' }} />
          </button>
        </div>
      </div>

      {/* Filters section removed as requested */}

      {/* Add custom CSS for scrollbar and improving touch experience */}
      <style jsx global>{`
        /* Hide default scrollbar but show custom one */
        .scrollbar-hide::-webkit-scrollbar {
          height: 4px;
          background: transparent;
        }
        .scrollbar-hide::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.1);
          border-radius: 4px;
        }
        .scrollbar-hide {
          scrollbar-width: thin;
          scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
        }
        .touch-pan-x {
          touch-action: pan-x;
          -webkit-overflow-scrolling: touch;
          scroll-behavior: smooth;
          scroll-snap-type: x mandatory;
        }
        .touch-pan-x > * {
          scroll-snap-align: center;
        }
      `}</style>
    </div>
  )
}
