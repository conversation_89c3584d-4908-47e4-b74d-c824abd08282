import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const table = searchParams.get('table')
    const recordId = searchParams.get('recordId')
    const action = searchParams.get('action')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // First check if audit_logs table exists
    const { data: tableExists, error: tableCheckError } = await adminClient
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'audit_logs')
      .single()

    if (tableCheckError || !tableExists) {
      console.log('Audit logs table does not exist yet')
      return NextResponse.json({
        logs: [],
        pagination: {
          total: 0,
          limit,
          offset,
          hasMore: false
        },
        message: 'Audit logs table not yet created. Please run the audit logs migration.'
      })
    }

    let query = adminClient
      .from('audit_logs')
      .select(`
        *,
        users:user_id(name, email)
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    // Apply filters
    if (table) {
      query = query.eq('table_name', table)
    }
    if (recordId) {
      query = query.eq('record_id', recordId)
    }
    if (action) {
      query = query.eq('action', action)
    }

    const { data: logs, error } = await query

    if (error) {
      console.error('Error fetching audit logs:', error)

      // Handle specific error cases
      if (error.message?.includes('relation "audit_logs" does not exist')) {
        return NextResponse.json({
          logs: [],
          pagination: {
            total: 0,
            limit,
            offset,
            hasMore: false
          },
          message: 'Audit logs table not yet created. Please run the audit logs migration.'
        })
      }

      return NextResponse.json({ error: 'Failed to fetch audit logs' }, { status: 500 })
    }

    // Get total count for pagination
    let countQuery = adminClient
      .from('audit_logs')
      .select('*', { count: 'exact', head: true })

    if (table) countQuery = countQuery.eq('table_name', table)
    if (recordId) countQuery = countQuery.eq('record_id', recordId)
    if (action) countQuery = countQuery.eq('action', action)

    const { count, error: countError } = await countQuery

    if (countError) {
      console.error('Error counting audit logs:', countError)
      // Continue with logs but without accurate count
    }

    return NextResponse.json({
      logs: logs || [],
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit
      }
    })

  } catch (error) {
    console.error('Error in audit logs API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
