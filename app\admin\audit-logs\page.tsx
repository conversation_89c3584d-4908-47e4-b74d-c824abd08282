"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useAuthDirect } from "@/context/auth-context-direct"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Badge,
} from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  FileText,
  Search,
  RefreshCw,
  Filter,
  ChevronLeft,
  ChevronRight,
  Database,
  User,
  Calendar,
  Activity,
  AlertCircle,
  CheckCircle,
  XCircle,
  Edit,
  Plus,
  Trash2
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface AuditLog {
  id: string
  table_name: string
  record_id: string
  action: 'INSERT' | 'UPDATE' | 'DELETE'
  old_values?: any
  new_values?: any
  user_id?: string
  created_at: string
  users?: {
    name?: string
    email?: string
  }
}

interface AuditLogsPagination {
  total: number
  limit: number
  offset: number
  hasMore: boolean
}

export default function AuditLogsPage() {
  const router = useRouter()
  const { userProfile } = useAuthDirect()
  const { toast } = useToast()
  
  const [logs, setLogs] = useState<AuditLog[]>([])
  const [pagination, setPagination] = useState<AuditLogsPagination>({
    total: 0,
    limit: 50,
    offset: 0,
    hasMore: false
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Filters
  const [tableFilter, setTableFilter] = useState<string>('')
  const [actionFilter, setActionFilter] = useState<string>('')
  const [recordIdFilter, setRecordIdFilter] = useState<string>('')
  const [searchTerm, setSearchTerm] = useState<string>('')

  // Available tables for filtering
  const commonTables = [
    'businesses',
    'users',
    'orders',
    'driver_profiles',
    'products',
    'business_requests',
    'feature_requests',
    'service_requests',
    'admin_notifications'
  ]

  useEffect(() => {
    fetchAuditLogs()
  }, [pagination.offset, tableFilter, actionFilter, recordIdFilter])

  const fetchAuditLogs = async () => {
    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        limit: pagination.limit.toString(),
        offset: pagination.offset.toString()
      })

      if (tableFilter) params.append('table', tableFilter)
      if (actionFilter) params.append('action', actionFilter)
      if (recordIdFilter) params.append('recordId', recordIdFilter)

      const response = await fetch(`/api/admin/audit-logs?${params}`)
      
      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          router.push("/login?redirectTo=/admin/audit-logs")
          return
        }
        
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to fetch audit logs")
      }
      
      const data = await response.json()
      setLogs(data.logs || [])
      setPagination(data.pagination)
    } catch (err: any) {
      console.error("Error fetching audit logs:", err)
      setError(err.message || "Failed to load audit logs")
      toast({
        variant: "destructive",
        title: "Error",
        description: err.message || "Failed to load audit logs"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = () => {
    setPagination(prev => ({ ...prev, offset: 0 }))
    fetchAuditLogs()
  }

  const handleClearFilters = () => {
    setTableFilter('')
    setActionFilter('')
    setRecordIdFilter('')
    setSearchTerm('')
    setPagination(prev => ({ ...prev, offset: 0 }))
  }

  const handleNextPage = () => {
    if (pagination.hasMore) {
      setPagination(prev => ({ ...prev, offset: prev.offset + prev.limit }))
    }
  }

  const handlePrevPage = () => {
    if (pagination.offset > 0) {
      setPagination(prev => ({ ...prev, offset: Math.max(0, prev.offset - prev.limit) }))
    }
  }

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'INSERT':
        return <Plus className="h-4 w-4 text-green-500" />
      case 'UPDATE':
        return <Edit className="h-4 w-4 text-blue-500" />
      case 'DELETE':
        return <Trash2 className="h-4 w-4 text-red-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getActionBadgeColor = (action: string) => {
    switch (action) {
      case 'INSERT':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'UPDATE':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'DELETE':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-GB', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  const formatTableName = (tableName: string) => {
    return tableName.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  // Filter logs by search term if provided
  const filteredLogs = logs.filter(log => {
    if (!searchTerm) return true
    const searchLower = searchTerm.toLowerCase()
    return (
      log.table_name.toLowerCase().includes(searchLower) ||
      log.record_id.toLowerCase().includes(searchLower) ||
      log.action.toLowerCase().includes(searchLower) ||
      log.users?.name?.toLowerCase().includes(searchLower) ||
      log.users?.email?.toLowerCase().includes(searchLower)
    )
  })

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <FileText className="h-6 w-6" />
            Audit Logs
          </h1>
          <p className="text-muted-foreground">
            Track all database changes and user actions across the platform
          </p>
        </div>
        <div className="flex items-center gap-2 mt-4 md:mt-0">
          <Button variant="outline" onClick={handleRefresh} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
          <CardDescription>
            Filter audit logs by table, action, record ID, or search terms
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="table-filter">Table</Label>
              <Select value={tableFilter} onValueChange={setTableFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All tables" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All tables</SelectItem>
                  {commonTables.map(table => (
                    <SelectItem key={table} value={table}>
                      {formatTableName(table)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="action-filter">Action</Label>
              <Select value={actionFilter} onValueChange={setActionFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All actions" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All actions</SelectItem>
                  <SelectItem value="INSERT">Insert</SelectItem>
                  <SelectItem value="UPDATE">Update</SelectItem>
                  <SelectItem value="DELETE">Delete</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="record-filter">Record ID</Label>
              <Input
                id="record-filter"
                placeholder="Enter record ID"
                value={recordIdFilter}
                onChange={(e) => setRecordIdFilter(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Search logs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          <div className="flex justify-between items-center mt-4">
            <div className="text-sm text-muted-foreground">
              Showing {filteredLogs.length} of {pagination.total} logs
            </div>
            <Button variant="outline" size="sm" onClick={handleClearFilters}>
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Error State */}
      {error && (
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Audit Logs Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Audit Trail
          </CardTitle>
          <CardDescription>
            Detailed log of all database operations and changes
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin mr-2" />
              <span>Loading audit logs...</span>
            </div>
          ) : filteredLogs.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No audit logs found matching your criteria</p>
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Timestamp</TableHead>
                      <TableHead>Table</TableHead>
                      <TableHead>Action</TableHead>
                      <TableHead>Record ID</TableHead>
                      <TableHead>User</TableHead>
                      <TableHead>Changes</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell className="font-mono text-sm">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            {formatTimestamp(log.created_at)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="font-mono">
                            {log.table_name}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getActionIcon(log.action)}
                            <Badge className={getActionBadgeColor(log.action)}>
                              {log.action}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell className="font-mono text-sm">
                          {log.record_id}
                        </TableCell>
                        <TableCell>
                          {log.users ? (
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4 text-gray-400" />
                              <div>
                                <div className="font-medium text-sm">
                                  {log.users.name || 'Unknown'}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {log.users.email}
                                </div>
                              </div>
                            </div>
                          ) : (
                            <span className="text-muted-foreground text-sm">System</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="max-w-xs">
                            {log.action === 'INSERT' && log.new_values && (
                              <div className="text-sm">
                                <span className="text-green-600 font-medium">Created:</span>
                                <div className="text-xs text-muted-foreground mt-1 truncate">
                                  {Object.keys(log.new_values).length} fields
                                </div>
                              </div>
                            )}
                            {log.action === 'UPDATE' && (log.old_values || log.new_values) && (
                              <div className="text-sm">
                                <span className="text-blue-600 font-medium">Modified:</span>
                                <div className="text-xs text-muted-foreground mt-1">
                                  {log.old_values && log.new_values ? 
                                    `${Object.keys(log.new_values).length} fields changed` :
                                    'Fields updated'
                                  }
                                </div>
                              </div>
                            )}
                            {log.action === 'DELETE' && log.old_values && (
                              <div className="text-sm">
                                <span className="text-red-600 font-medium">Deleted:</span>
                                <div className="text-xs text-muted-foreground mt-1">
                                  Record removed
                                </div>
                              </div>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-muted-foreground">
                  Showing {pagination.offset + 1} to {Math.min(pagination.offset + pagination.limit, pagination.total)} of {pagination.total} logs
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePrevPage}
                    disabled={pagination.offset === 0}
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleNextPage}
                    disabled={!pagination.hasMore}
                  >
                    Next
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
