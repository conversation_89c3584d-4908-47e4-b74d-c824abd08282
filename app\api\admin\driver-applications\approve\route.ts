import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function POST(request: NextRequest) {
  try {
    const { driverId, userEmail } = await request.json()

    if (!driverId || !userEmail) {
      return NextResponse.json(
        { error: "Driver ID and user email are required" },
        { status: 400 }
      )
    }

    // Get session for admin user info
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Get driver profile information for notification
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('first_name, last_name, email, vehicle_type')
      .eq('id', driverId)
      .single()

    if (driverError || !driverProfile) {
      console.error('Error fetching driver profile:', driverError)
      return NextResponse.json(
        { error: "Driver not found" },
        { status: 404 }
      )
    }

    // Update driver profile to verified
    const { error: updateError } = await supabase
      .from('driver_profiles')
      .update({
        is_verified: true,
        is_active: true,
        verification_date: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', driverId)

    if (updateError) {
      console.error('Error updating driver profile:', updateError)
      return NextResponse.json(
        { error: "Failed to approve driver" },
        { status: 500 }
      )
    }

    // Log the approval activity
    const { error: activityError } = await supabase
      .from('driver_activity_log')
      .insert({
        driver_id: driverId,
        activity_type: 'application_approved',
        timestamp: new Date().toISOString(),
        notes: 'Driver application approved by Loop admin'
      })

    if (activityError) {
      console.error('Error logging driver activity:', activityError)
      // Don't fail the request for this, just log it
    }

    // Create admin notification for driver approval
    try {
      const { data: adminUsers } = await supabase
        .from('users')
        .select('id')
        .eq('role', 'admin')
        .neq('id', session.user.id) // Don't notify the admin who performed the action

      if (adminUsers && adminUsers.length > 0) {
        const notifications = adminUsers.map(admin => ({
          admin_user_id: admin.id,
          type: 'driver_approved',
          title: 'Driver Application Approved',
          message: `${driverProfile.first_name} ${driverProfile.last_name} has been approved as a driver by ${session.user.email}`,
          action_url: `/admin/driver-applications/${driverId}`,
          priority: 'medium',
          related_table: 'driver_profiles',
          related_record_id: driverId,
          metadata: {
            driver_name: `${driverProfile.first_name} ${driverProfile.last_name}`,
            driver_email: driverProfile.email,
            vehicle_type: driverProfile.vehicle_type,
            approved_by: session.user.email
          }
        }))

        await supabase
          .from('admin_notifications')
          .insert(notifications)

        console.log(`Created admin notifications for ${adminUsers.length} admin users`)
      }
    } catch (notificationError) {
      console.error('Error creating admin notification:', notificationError)
      // Don't fail the request if notification fails
    }

    // TODO: Send approval email to driver
    // For now, we'll just log that an email should be sent
    console.log(`TODO: Send approval email to ${userEmail} for driver ${driverId}`)

    // In a real implementation, you would send an email here:
    /*
    await sendDriverApprovalEmail({
      email: userEmail,
      driverId: driverId,
      loginUrl: `${process.env.NEXT_PUBLIC_APP_URL}/driver/login`
    })
    */

    return NextResponse.json({
      success: true,
      message: "Driver approved successfully",
      driverId: driverId
    })

  } catch (error) {
    console.error('Error in driver approval:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// TODO: Implement email sending function
/*
async function sendDriverApprovalEmail({ email, driverId, loginUrl }: {
  email: string
  driverId: string
  loginUrl: string
}) {
  // Implementation would use your email service (e.g., SendGrid, AWS SES, etc.)
  // Email template would include:
  // - Congratulations message
  // - Login instructions
  // - Next steps (applying to businesses)
  // - Contact information for support
}
*/
