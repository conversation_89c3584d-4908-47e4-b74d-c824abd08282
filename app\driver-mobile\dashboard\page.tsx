"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { ArrowUpRight, DollarSign, Package, Clock, MapPin, Star, RefreshCw, AlertCircle, Navigation, Bell, AlertTriangle, ChevronUp, ChevronDown, Zap, WifiOff } from "lucide-react"
import WheelLogoIcon from "@/components/wheel-logo-icon"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { NotificationPermission } from "@/components/notification-permission"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { DriverDashboardHeader } from "@/components/driver-dashboard-header"
import { DriverMobileNavigation } from "@/components/driver-mobile-navigation"
import { DriverMobileContainer } from "@/components/driver-mobile-container"
import { SwipeableOrderCard } from "@/components/driver/swipeable-order-card"

import { EnhancedMobileRealtime } from "@/components/enhanced-mobile-realtime"
import { EnhancedLocationTracker } from "@/components/enhanced-location-tracker"
import { notificationService } from "@/services/notification-service"
import { toast } from "sonner"
import { useSupabaseRealtime } from "@/hooks/use-supabase-realtime"

interface DriverDashboardData {
  driver: {
    id: string
    authId: string
    name: string
    isVerified: boolean
    isActive: boolean
    vehicleType: string
    totalDeliveries: number
    averageRating: number
    memberSince: string
    averageDeliveriesPerDay: number
  }
  status: {
    isOnline: boolean
    isOnDelivery: boolean
    isOnShift?: boolean
    lastStatusChange: string | null
    hasLocation: boolean
    locationUpdatedAt: string | null
  }
  currentOrder: any | null
  earnings: {
    today: number
    thisWeek: number
    currency: string
  }
  stats: {
    todayDeliveries: number
    availableOrders: number
    totalDeliveries: number
    averageRating: number
  }
  recentDeliveries: any[]
  timestamp: string
}

interface AvailableOrder {
  id: number
  order_number: string
  business_name: string
  customer_name: string
  delivery_address: string
  postcode: string
  parish: string
  delivery_type: string
  total: number
  delivery_fee: number
  distance: number | null
  delivery_distance_km: number | null
  estimated_delivery_time: string | number | null
  estimatedPickupTime: number | null
  itemCount: number
  totalItems: number
  businesses: {
    name: string
    address: string
    phone: string
  }
}

interface OrderItem {
  id: string
  name: string
  quantity: number
  price: number
  image_url?: string
  customizations?: any
  special_instructions?: string
  products?: {
    id: string
    name: string
    description: string
    category: string
  }
}

interface AvailableOrdersData {
  availableOrders: AvailableOrder[]
  currentOrder: any | null
  isOnDelivery: boolean
  driverLocation: {
    lat: number
    lng: number
  } | null
}

export default function DriverMobileDashboard() {
  const [dashboardData, setDashboardData] = useState<DriverDashboardData | null>(null)
  const [availableOrders, setAvailableOrders] = useState<AvailableOrdersData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)
  const [currentTime, setCurrentTime] = useState(new Date())
  const [notificationsEnabled, setNotificationsEnabled] = useState(false)
  const [isEnablingNotifications, setIsEnablingNotifications] = useState(false)
  const [expandedOrders, setExpandedOrders] = useState<Set<number>>(new Set())
  const [orderItems, setOrderItems] = useState<Record<number, OrderItem[]>>({})
  const [loadingItems, setLoadingItems] = useState<Set<number>>(new Set())
  const [processingOrders, setProcessingOrders] = useState<Set<number>>(new Set())

  // Real-time subscription for orders
  const {
    data: realtimeOrders,
    loading: realtimeLoading,
    error: realtimeError,
  } = useSupabaseRealtime<any>(
    'orders',
    `
      id,
      order_number,
      business_id,
      business_name,
      customer_name,
      customer_phone,
      delivery_address,
      postcode,
      parish,
      delivery_type,
      total,
      delivery_fee,
      status,
      driver_id,
      preparation_time,
      estimated_delivery_time,
      ready_time,
      created_at,
      cart_id,
      businesses!inner (
        id,
        name,
        address,
        location,
        phone,
        latitude,
        coordinates
      )
    `,
    [
      { table: 'orders', event: 'UPDATE', filter: 'status=eq.offered' },
      { table: 'orders', event: 'INSERT', filter: 'status=eq.offered' },
      { table: 'orders', event: 'UPDATE', filter: 'status=eq.assigned' },
      { table: 'orders', event: 'DELETE' },
    ],
    {
      initialFilter: { status: 'offered', driver_id: null },
      orderBy: 'ready_time',
      orderDirection: 'asc',
      limit: 10
    }
  )

  // Update available orders when real-time data changes
  useEffect(() => {
    if (realtimeOrders && !realtimeLoading) {
      // Filter for offered orders with no driver assigned
      const offeredOrders = realtimeOrders.filter(order =>
        order.status === 'offered' && !order.driver_id
      )

      // Update available orders state
      setAvailableOrders(prev => ({
        ...prev,
        availableOrders: offeredOrders.map(order => ({
          ...order,
          itemCount: 0, // Will be calculated when expanded
          totalItems: 0, // Will be calculated when expanded
          distance: null, // Will be calculated if driver location available
          estimatedPickupTime: null // Will be calculated if driver location available
        }))
      }))

      // Show toast for new orders (only if we already have data loaded)
      if (availableOrders && offeredOrders.length > (availableOrders.availableOrders?.length || 0)) {
        const newOrdersCount = offeredOrders.length - (availableOrders.availableOrders?.length || 0)
        if (newOrdersCount > 0) {
          toast.success(`${newOrdersCount} new order${newOrdersCount > 1 ? 's' : ''} available!`, {
            description: "Check the available orders section below.",
            duration: 4000,
          })
        }
      }
    }
  }, [realtimeOrders, realtimeLoading])

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)

    return () => clearInterval(timer)
  }, [])

  // Check notification status on load
  useEffect(() => {
    const checkNotificationStatus = async () => {
      await notificationService.initialize()
      setNotificationsEnabled(notificationService.notificationsEnabled)
    }
    checkNotificationStatus()
  }, [])

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/driver/dashboard')
      if (!response.ok) {
        throw new Error(`Failed to fetch dashboard data: ${response.statusText}`)
      }
      const data = await response.json()
      setDashboardData(data)
    } catch (err) {
      console.error('Error fetching dashboard data:', err)
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data')
    }
  }

  // Fetch available orders
  const fetchAvailableOrders = async () => {
    try {
      const response = await fetch('/api/driver/available-orders')
      if (!response.ok) {
        throw new Error(`Failed to fetch available orders: ${response.statusText}`)
      }
      const data = await response.json()
      setAvailableOrders(data)
    } catch (err) {
      console.error('Error fetching available orders:', err)
      // Don't set error for available orders as it's not critical
    }
  }

  // Update driver shift status - Simplified on-shift/off-shift toggle
  const updateDriverStatus = async (isOnShift: boolean) => {
    setIsUpdatingStatus(true)
    try {
      const response = await fetch('/api/driver/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isOnShift, action: 'toggle' }),
      })

      if (!response.ok) {
        throw new Error(`Failed to update status: ${response.statusText}`)
      }

      const result = await response.json()

      // Update local state
      if (dashboardData) {
        setDashboardData({
          ...dashboardData,
          status: {
            ...dashboardData.status,
            isOnShift: result.status.isOnShift,
            lastStatusChange: result.status.lastStatusChange
          }
        })
      }

      // Refresh available orders when starting shift
      if (isOnShift) {
        await fetchAvailableOrders()
      }

    } catch (err) {
      console.error('Error updating driver status:', err)
      setError(err instanceof Error ? err.message : 'Failed to update status')
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  // Start shift - Begin accepting orders
  const startShift = async () => {
    setIsUpdatingStatus(true)
    try {
      const response = await fetch('/api/driver/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isOnline: true, action: 'start_shift' }),
      })

      if (!response.ok) {
        throw new Error(`Failed to start shift: ${response.statusText}`)
      }

      const result = await response.json()

      // Update local state
      if (dashboardData) {
        setDashboardData({
          ...dashboardData,
          status: {
            ...dashboardData.status,
            isOnline: true,
            isOnShift: true,
            lastStatusChange: result.status.lastStatusChange
          }
        })
      }

      // Refresh available orders
      fetchAvailableOrders()

    } catch (err) {
      console.error('Error starting shift:', err)
      setError(err instanceof Error ? err.message : 'Failed to start shift')
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  // Toggle shift status - Start or end shift
  const toggleShift = async (shouldBeOnShift: boolean) => {
    // If ending shift, show confirmation dialog
    if (!shouldBeOnShift) {
      const confirmed = window.confirm(
        `Are you sure you want to end your shift?\n\n` +
        `Today's Summary:\n` +
        `• Earnings: ${formatCurrency(dashboardData?.earnings.today || 0)}\n` +
        `• Deliveries: ${dashboardData?.stats.todayDeliveries || 0}\n\n` +
        `You will remain online but stop receiving new orders.`
      )

      if (!confirmed) return
    }

    setIsUpdatingStatus(true)
    try {
      const action = shouldBeOnShift ? 'start_shift' : 'end_shift'
      const response = await fetch('/api/driver/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isOnline: true, // Always stay online when toggling shift
          action
        }),
      })

      if (!response.ok) {
        throw new Error(`Failed to ${action.replace('_', ' ')}: ${response.statusText}`)
      }

      const result = await response.json()

      // Show appropriate message
      if (shouldBeOnShift) {
        toast.success("Shift started!", {
          description: "You're now available to receive orders.",
          duration: 3000,
        })
      } else {
        toast.success("Shift ended!", {
          description: `You earned ${formatCurrency(dashboardData?.earnings.today || 0)} today with ${dashboardData?.stats.todayDeliveries || 0} deliveries.`,
          duration: 5000,
        })
      }

      // Update local state
      if (dashboardData) {
        setDashboardData({
          ...dashboardData,
          status: {
            ...dashboardData.status,
            isOnShift: result.status.isOnShift,
            lastStatusChange: result.status.lastStatusChange
          }
        })
      }

      // Refresh available orders when starting shift
      if (shouldBeOnShift) {
        fetchAvailableOrders()
      }

    } catch (err) {
      console.error('Error toggling shift:', err)
      setError(err instanceof Error ? err.message : `Failed to ${shouldBeOnShift ? 'start' : 'end'} shift`)
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  // Enable notifications
  const enableNotifications = async () => {
    setIsEnablingNotifications(true)
    try {
      await notificationService.initialize()

      if (notificationService.permissionStatus !== 'granted') {
        const permission = await notificationService.requestPermission()
        if (permission !== 'granted') {
          throw new Error('Notification permission denied')
        }
      }

      // Subscribe to notifications (service handles driver mobile differently)
      await notificationService.subscribe()

      setNotificationsEnabled(notificationService.notificationsEnabled)

      // Show success message
      toast.success("Notifications enabled!", {
        description: "You'll receive updates about new delivery requests.",
        duration: 3000,
      })

    } catch (err) {
      console.error('Error enabling notifications:', err)
      setError(err instanceof Error ? err.message : 'Failed to enable notifications')
    } finally {
      setIsEnablingNotifications(false)
    }
  }

  // Toggle order expansion and fetch items if needed
  const toggleOrderExpansion = async (orderId: number) => {
    const newExpandedOrders = new Set(expandedOrders)

    if (expandedOrders.has(orderId)) {
      newExpandedOrders.delete(orderId)
    } else {
      newExpandedOrders.add(orderId)

      // Fetch order items if not already loaded
      if (!orderItems[orderId]) {
        const newLoadingItems = new Set(loadingItems)
        newLoadingItems.add(orderId)
        setLoadingItems(newLoadingItems)

        try {
          const response = await fetch(`/api/driver/orders/${orderId}/items`)
          if (response.ok) {
            const data = await response.json()
            setOrderItems(prev => ({
              ...prev,
              [orderId]: data.items || []
            }))
          } else {
            console.error('Failed to fetch order items:', response.statusText)
            toast.error("Failed to load order items", {
              description: "Please try again.",
              duration: 3000,
            })
          }
        } catch (err) {
          console.error('Error fetching order items:', err)
          toast.error("Failed to load order items", {
            description: "Please check your connection and try again.",
            duration: 3000,
          })
        } finally {
          const newLoadingItems = new Set(loadingItems)
          newLoadingItems.delete(orderId)
          setLoadingItems(newLoadingItems)
        }
      }
    }

    setExpandedOrders(newExpandedOrders)
  }

  // Accept an order
  const acceptOrder = async (orderId: number) => {
    try {
      const response = await fetch('/api/driver/accept-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ orderId }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to accept order: ${response.statusText}`)
      }

      const result = await response.json()

      // Refresh dashboard and available orders
      await Promise.all([fetchDashboardData(), fetchAvailableOrders()])

      // Show success message
      toast.success("Order accepted!", {
        description: "You can now start the delivery.",
        duration: 3000,
      })

    } catch (err) {
      console.error('Error accepting order:', err)
      toast.error("Failed to accept order", {
        description: err instanceof Error ? err.message : 'Please try again.',
        duration: 4000,
      })
    }
  }

  // Decline an order
  const declineOrder = async (orderId: number) => {
    try {
      const response = await fetch('/api/driver/decline-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ orderId }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to decline order: ${response.statusText}`)
      }

      const result = await response.json()

      // Remove the declined order from the UI immediately
      if (availableOrders) {
        const updatedOrders = availableOrders.availableOrders.filter(order => order.id !== orderId)
        setAvailableOrders({
          ...availableOrders,
          availableOrders: updatedOrders
        })
      }

      // Also refresh from server to ensure consistency
      await fetchAvailableOrders()

      // Show success message
      toast.success("Order declined", {
        description: "The order has been removed from your available orders.",
        duration: 3000,
      })

    } catch (err) {
      console.error('Error declining order:', err)
      toast.error("Failed to decline order", {
        description: err instanceof Error ? err.message : 'Please try again.',
        duration: 4000,
      })
    }
  }

  // Initial data load
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      setError(null)

      try {
        await Promise.all([fetchDashboardData(), fetchAvailableOrders()])
      } catch (err) {
        console.error('Error loading initial data:', err)
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [])

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isLoading) {
        fetchDashboardData()
        fetchAvailableOrders()
      }
    }, 30000)

    return () => clearInterval(interval)
  }, [isLoading])

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount)
  }

  const getGreeting = () => {
    const hour = currentTime.getHours()
    if (hour < 12) return "Good morning"
    if (hour < 17) return "Good afternoon"
    return "Good evening"
  }

  if (isLoading) {
    return (
      <div className="p-4 space-y-6">
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-500">Loading dashboard...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-4 space-y-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button onClick={() => window.location.reload()} className="w-full">
          Retry
        </Button>
      </div>
    )
  }

  if (!dashboardData) {
    return (
      <div className="p-4 space-y-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>No dashboard data available</AlertDescription>
        </Alert>
      </div>
    )
  }

  // Debug logging
  console.log("📊 Dashboard render data:", {
    earnings: dashboardData?.earnings,
    stats: dashboardData?.stats,
    status: dashboardData?.status,
    isOnShift: dashboardData?.status?.isOnShift,
    availableOrdersCount: availableOrders?.availableOrders?.length
  })

  return (
    <DriverMobileContainer>
      <DriverDashboardHeader
        driver={dashboardData.driver}
        status={dashboardData.status}
        onStatusChange={updateDriverStatus}
        isUpdatingStatus={isUpdatingStatus}
      />

      <div className="p-4 space-y-4">

        {/* Enhanced Real-time Features - Only when on shift */}
        {dashboardData.status.isOnShift && (
          <EnhancedMobileRealtime
            driverId={parseInt(dashboardData.driver.id)}
            availableOrdersCount={availableOrders ? availableOrders.availableOrders.length : dashboardData.stats.availableOrders}
            isOnShift={dashboardData.status.isOnShift || false}
            onShiftToggle={toggleShift}
            isUpdatingStatus={isUpdatingStatus}
            onNewOrder={(order) => {
              // Refresh available orders when new order comes in
              fetchAvailableOrders()
            }}
            onOrderUpdate={(order) => {
              // Refresh dashboard data when order updates
              fetchDashboardData()
            }}
          />
        )}

        {/* Offline Status Card - Only when offline */}
        {!dashboardData.status.isOnline && (
          <Card className="border-2 border-gray-300 bg-gray-50">
            <CardContent className="p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    <Zap className="h-4 w-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-700">Status</span>
                  </div>
                  <Badge variant="outline" className="bg-gray-50 text-gray-600 border-gray-300 text-xs px-2 py-0.5">
                    <WifiOff className="h-4 w-4 text-gray-400 mr-1" />
                    <span>Offline</span>
                  </Badge>
                </div>
                <div className="text-center">
                  <div className="text-sm text-gray-600">
                    Go online to see live orders
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Enhanced Location Tracking */}
        {dashboardData.status.isOnDelivery && dashboardData.currentOrder && (
          <EnhancedLocationTracker
            orderId={parseInt(dashboardData.currentOrder.id)}
            driverId={parseInt(dashboardData.driver.id)}
            isActive={dashboardData.status.isOnDelivery}
            onLocationUpdate={(location) => {
              console.log('Location updated:', location)
            }}
          />
        )}

        {/* Enable Notifications */}
        {!notificationsEnabled && (
          <Card className="bg-green-50 border-green-200">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                <Bell className="h-5 w-5 text-green-600 mt-0.5" />
                <div className="flex-1">
                  <h3 className="font-semibold text-green-900">Enable notifications</h3>
                  <p className="text-sm text-green-700 mb-3">Get notified about new delivery requests and important updates.</p>
                  <Button
                    onClick={enableNotifications}
                    disabled={isEnablingNotifications}
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    {isEnablingNotifications ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Enabling...
                      </>
                    ) : (
                      'Enable Notifications'
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Error Alert */}
        {error && (
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">{error}</AlertDescription>
          </Alert>
        )}

        {/* Current Delivery - Priority #1 */}
        {dashboardData.status.isOnDelivery && dashboardData.currentOrder ? (
          <Card className="border-2 border-blue-500 bg-blue-50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                  <span className="font-semibold text-blue-900">ACTIVE DELIVERY</span>
                </div>
                <Badge className="bg-blue-600 text-white">
                  {dashboardData.currentOrder.status.replace('_', ' ').toUpperCase()}
                </Badge>
              </div>

              <div className="space-y-2 mb-4">
                <div>
                  <p className="text-sm text-blue-700">Order #{dashboardData.currentOrder.order_number}</p>
                  <p className="font-medium text-blue-900">{dashboardData.currentOrder.business_name}</p>
                </div>
                <div>
                  <p className="text-sm text-blue-700">Deliver to:</p>
                  <p className="font-medium text-blue-900">{dashboardData.currentOrder.customer_name}</p>
                  <p className="text-sm text-blue-800">{dashboardData.currentOrder.delivery_address}</p>
                </div>
              </div>

              <Link href={`/driver-mobile/deliveries/${dashboardData.currentOrder.order_number}`}>
                <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium">
                  <Navigation className="h-4 w-4 mr-2" />
                  Continue Delivery
                </Button>
              </Link>
            </CardContent>
          </Card>
        ) : null}

        {/* Quick Stats - What Matters Today */}
        <div className="grid grid-cols-3 gap-3">
          <Link href="/driver-mobile/earnings">
            <Card className="bg-green-50 border-green-200 hover:border-green-300 transition-colors cursor-pointer">
              <CardContent className="p-3 text-center">
                <div className="text-2xl font-bold text-green-700">
                  {formatCurrency(dashboardData?.earnings?.today || 0)}
                </div>
                <div className="text-xs text-green-600 font-medium">Today's Earnings</div>
              </CardContent>
            </Card>
          </Link>

          <Link href="/driver-mobile/earnings">
            <Card className="bg-blue-50 border-blue-200 hover:border-blue-300 transition-colors cursor-pointer">
              <CardContent className="p-3 text-center">
                <div className="text-2xl font-bold text-blue-700">
                  {dashboardData?.stats?.todayDeliveries || 0}
                </div>
                <div className="text-xs text-blue-600 font-medium">Today's Deliveries</div>
              </CardContent>
            </Card>
          </Link>

          <Link href="/driver-mobile/orders">
            <Card className="bg-blue-50 border-blue-200 hover:border-blue-300 transition-colors cursor-pointer">
              <CardContent className="p-3 text-center">
                <div className="text-2xl font-bold text-blue-700">
                  {availableOrders ? availableOrders.availableOrders.length : (dashboardData?.stats?.availableOrders || 0)}
                </div>
                <div className="text-xs text-blue-600 font-medium">Available</div>
              </CardContent>
            </Card>
          </Link>
        </div>

        {/* Start Shift Action - When offline OR online but not on shift */}
        {(!dashboardData.status.isOnline || (dashboardData.status.isOnline && !dashboardData.status.isOnShift)) && !dashboardData.status.isOnDelivery && (
          <Button
            className="w-full h-12 text-lg font-semibold bg-green-600 hover:bg-green-700 text-white"
            onClick={() => {
              if (!dashboardData.status.isOnline) {
                // If offline, go online first (but don't start shift yet)
                updateDriverStatus(true)
              } else {
                // If online but not on shift, start shift
                startShift()
              }
            }}
            disabled={isUpdatingStatus}
          >
            {isUpdatingStatus ? (
              <>
                <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                {!dashboardData.status.isOnline ? 'Going Online...' : 'Starting Shift...'}
              </>
            ) : (
              <>
                <Package className="h-5 w-5 mr-2" />
                {!dashboardData.status.isOnline ? 'Go Online' : 'Start Shift'}
              </>
            )}
          </Button>
        )}

        {/* Available Orders - Driver Priority View - Only when on shift */}
        {availableOrders && availableOrders.availableOrders.length > 0 && dashboardData.status.isOnline && dashboardData.status.isOnShift && !dashboardData.status.isOnDelivery && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">New Orders</h3>
            </div>

            {availableOrders.availableOrders.slice(0, 3).map((order) => (
              <SwipeableOrderCard
                key={order.id}
                order={order}
                isExpanded={expandedOrders.has(order.id)}
                onToggleExpansion={() => toggleOrderExpansion(order.id)}
                onAccept={() => acceptOrder(order.id)}
                onDecline={() => declineOrder(order.id)}
                orderItems={orderItems[order.id]}
                loadingItems={loadingItems.has(order.id)}
                formatCurrency={formatCurrency}
              />
            ))}

            {availableOrders.availableOrders.length > 3 && (
              <Link href="/driver-mobile/orders">
                <Button variant="outline" className="w-full border-blue-200 text-blue-700 hover:bg-blue-50">
                  View All {availableOrders.availableOrders.length} Orders
                </Button>
              </Link>
            )}
          </div>
        )}

        {/* Market Research View - Online but not on shift */}
        {availableOrders && availableOrders.availableOrders.length > 0 && dashboardData.status.isOnline && !dashboardData.status.isOnShift && !dashboardData.status.isOnDelivery && (
          <Card className="border-2 border-blue-200 bg-blue-50">
            <CardContent className="p-4 text-center">
              <div className="flex items-center justify-center mb-2">
                <Package className="h-8 w-8 text-blue-600 mr-3" />
                <div>
                  <div className="text-2xl font-bold text-blue-900">
                    {availableOrders.availableOrders.length}
                  </div>
                  <div className="text-sm text-blue-700 font-medium">
                    Order{availableOrders.availableOrders.length !== 1 ? 's' : ''} Available
                  </div>
                </div>
              </div>
              <p className="text-sm text-blue-600">
                Start your shift to accept orders
              </p>
            </CardContent>
          </Card>
        )}

        {/* Waiting for Orders */}
        {dashboardData.status.isOnline && dashboardData.status.isOnShift && !dashboardData.status.isOnDelivery && (!availableOrders || availableOrders.availableOrders.length === 0) && (
          <Card className="border-2 border-dashed border-gray-300 bg-gray-50">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3">
                <Package className="h-6 w-6 text-gray-400" />
              </div>
              <p className="font-medium text-gray-700 mb-1">Waiting for orders...</p>
              <p className="text-sm text-gray-500">You'll be notified when new deliveries are available</p>
            </CardContent>
          </Card>
        )}

        {/* Recent Activity - Only if relevant */}
        {dashboardData.recentDeliveries && dashboardData.recentDeliveries.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Recent Deliveries</h3>
              <div className="flex items-center space-x-3 text-xs text-gray-500">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 rounded-full bg-green-500"></div>
                  <span>Delivered</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                  <span>In Progress</span>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              {dashboardData.recentDeliveries.slice(0, 2).map((delivery) => (
                <Card key={delivery.id} className="bg-white border-gray-200 hover:border-gray-300 transition-colors duration-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          delivery.status === 'delivered' ? 'bg-green-500' : 'bg-blue-500'
                        }`}></div>
                        <div>
                          <p className="font-medium text-gray-900">#{delivery.order_number}</p>
                          <p className="text-sm text-gray-600">{delivery.business_name}</p>
                          <p className="text-xs text-gray-500 capitalize">{delivery.status.replace('_', ' ')}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-gray-900">{formatCurrency(delivery.delivery_fee)}</p>
                        <p className="text-xs text-gray-500">
                          {new Date(delivery.updated_at).toLocaleDateString('en-GB')}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {dashboardData.recentDeliveries.length > 2 && (
                <Link href="/driver-mobile/earnings">
                  <Button variant="outline" className="w-full text-gray-600 mt-3">
                    View All Deliveries
                  </Button>
                </Link>
              )}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className={`grid gap-4 ${
          dashboardData.status.isOnline && !dashboardData.status.isOnShift && !dashboardData.status.isOnDelivery ? 'grid-cols-3' :
          'grid-cols-2'
        }`}>
          <Link href="/driver-mobile/earnings">
            <Button
              variant="outline"
              className="h-14 flex-col space-y-1.5 border-2 border-gray-200 hover:border-green-300 hover:bg-green-50 transition-all duration-200"
            >
              <DollarSign className="h-5 w-5 text-green-600" />
              <span className="text-xs font-medium text-gray-700">Earnings</span>
            </Button>
          </Link>

          <Button
            variant="outline"
            className="h-14 flex-col space-y-1.5 border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200"
            onClick={() => {
              fetchDashboardData()
              fetchAvailableOrders()
            }}
            disabled={isLoading}
          >
            <RefreshCw className={`h-5 w-5 text-blue-600 ${isLoading ? 'animate-spin' : ''}`} />
            <span className="text-xs font-medium text-gray-700">Refresh</span>
          </Button>



          {/* End Shift - Only when on shift but not on delivery */}
          {dashboardData.status.isOnShift && !dashboardData.status.isOnDelivery && (
            <Button
              variant="outline"
              className="h-14 flex-col space-y-1.5 border-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-all duration-200"
              onClick={() => updateDriverStatus(false)}
              disabled={isUpdatingStatus}
            >
              {isUpdatingStatus ? (
                <RefreshCw className="h-4 w-4 text-gray-600 animate-spin" />
              ) : (
                <WifiOff className="h-5 w-5 text-gray-600" />
              )}
              <span className="text-xs font-medium text-gray-700">End Shift</span>
            </Button>
          )}
        </div>

        {/* Driver Performance Summary */}
        <Card className="bg-gray-50 border-gray-200">
          <CardContent className="p-4">
            <h3 className="font-semibold text-gray-900 mb-3">Your Performance</h3>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-lg font-bold text-gray-900">{dashboardData.driver.totalDeliveries}</div>
                <div className="text-xs text-gray-600">Total Deliveries</div>
              </div>
              <div>
                <div className="text-lg font-bold text-gray-900">
                  {dashboardData.driver.averageRating ? dashboardData.driver.averageRating.toFixed(1) : 'N/A'}
                </div>
                <div className="text-xs text-gray-600">Rating</div>
              </div>
              <div>
                <div className="text-lg font-bold text-gray-900">{dashboardData.driver.averageDeliveriesPerDay}</div>
                <div className="text-xs text-gray-600">Avg/Day</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bottom Spacing for Mobile Navigation */}
        <div className="h-20"></div>
      </div>

      <DriverMobileNavigation />
    </DriverMobileContainer>
  )
}
